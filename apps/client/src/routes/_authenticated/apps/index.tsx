import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/card'
import { Button } from '@coozf/ui/button'
import { Input } from '@coozf/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/table'
import { Badge } from '@coozf/ui/badge'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@coozf/ui/alert-dialog'
import { Plus, Search, Edit, Trash2, Copy } from 'lucide-react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import { Link } from '@tanstack/react-router'

function ApplicationsPage() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')

  // 获取应用列表
  const {
    data: applicationsData,
    isLoading,
    refetch,
  } = trpc.application.list.useQuery({
    page,
    pageSize: 10,
    search: search || undefined,
  })

  // 删除应用
  const deleteApplicationMutation = trpc.application.delete.useMutation({
    onSuccess: () => {
      toast.success('应用删除成功')
      refetch()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const handleDelete = (id: string) => {
    deleteApplicationMutation.mutate({ applicationId: id })
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const formatBalance = (balance: string) => {
    return `${parseFloat(balance).toFixed(2)} 蚁贝`
  }

  const formatTraffic = (trafficKB: number) => {
    if (trafficKB < 1024) {
      return `${trafficKB} KB`
    } else if (trafficKB < 1024 * 1024) {
      return `${(trafficKB / 1024).toFixed(2)} MB`
    } else {
      return `${(trafficKB / (1024 * 1024)).toFixed(2)} GB`
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">应用管理</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">加载中...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">应用管理</h1>
        <Button asChild>
          <Link to="/apps/create">
            <Plus className="w-4 h-4 mr-2" />
            创建应用
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>应用列表</CardTitle>
          <CardDescription>管理您的应用程序，查看使用情况和配置信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索应用名称..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {applicationsData?.items.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">还没有创建任何应用</p>
              <Button asChild>
                <Link to="/apps/create">
                  <Plus className="w-4 h-4 mr-2" />
                  创建第一个应用
                </Link>
              </Button>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>应用名称</TableHead>
                    <TableHead>应用ID</TableHead>
                    <TableHead>余额</TableHead>
                    <TableHead>流量</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {applicationsData?.items.map((app) => (
                    <TableRow key={app.id}>
                      <TableCell>
                        <div>
                          <Link to={'/apps/$id'} params={{ id: app.id }} className="font-medium">
                            {app.name}
                          </Link>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <code className="text-sm bg-muted px-2 py-1 rounded">{app.id}</code>
                          <Button variant="ghost" size="sm" onClick={() => copyToClipboard(app.id)}>
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{formatBalance(app.balance)}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{formatTraffic(0)}</Badge>
                      </TableCell>
                      <TableCell>{new Date(app.createdAt).toLocaleDateString('zh-CN')}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link to="/apps/$id/edit" params={{ id: app.id }}>
                              <Edit className="w-3 h-3" />
                            </Link>
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>确认删除</AlertDialogTitle>
                                <AlertDialogDescription>
                                  您确定要删除应用 "{app.name}" 吗？此操作无法撤销。
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>取消</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(app.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  删除
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {applicationsData && applicationsData.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    共 {applicationsData.total} 个应用，第 {page} 页，共 {applicationsData.totalPages} 页
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page <= 1}>
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(page + 1)}
                      disabled={page >= applicationsData.totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/apps/')({
  component: ApplicationsPage,
})
