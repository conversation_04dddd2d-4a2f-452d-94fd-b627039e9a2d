import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/card'
import { Button } from '@coozf/ui/button'
import { Badge } from '@coozf/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@coozf/ui/tabs'
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from '@coozf/ui/chart'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@coozf/ui/alert-dialog'
import { trpc } from '@/lib/trpc'
import { cn } from '@/lib/utils'
import { LineChart, Line, XAxis, YAxis, CartesianGrid } from 'recharts'
import { toast } from 'sonner'
import {
  Copy,
  Settings,
  Coins,
  Users,
  Zap,
  Activity,
  Eye,
  EyeOff,
  TrendingUp,
  TrendingDown,
  Minus,
  Shield,
  CreditCard,
  RefreshCw,
} from 'lucide-react'
import { Link } from '@tanstack/react-router'

export const Route = createFileRoute('/_authenticated/apps/$id/')({
  component: ApplicationDetailPage,
})

const chartConfig = {
  apiCalls: {
    label: '接口调用',
    color: 'hsl(var(--chart-1))',
  },
  accountCount: {
    label: '账号',
    color: 'hsl(var(--chart-2))',
  },
  trafficGB: {
    label: '流量',
    color: 'hsl(var(--chart-3))',
  },
} satisfies ChartConfig

function ApplicationDetailPage() {
  const { id } = Route.useParams()
  const [showSecret, setShowSecret] = useState(false)
  const [transactionPage, setTransactionPage] = useState(1)
  const [apiPage, setApiPage] = useState(1)
  const [newSecret, setNewSecret] = useState<string | null>(null)
  const pageSize = 10

  // 获取应用详情
  const { data: app, isLoading: appLoading } = trpc.application.byId.useQuery({ applicationId: id })

  // 获取应用蚁贝余额
  const { data: balanceData } = trpc.balance.getApplicationBalance.useQuery({ applicationId: id }, { enabled: !!id })

  // 获取应用统计数据
  const { data: statsData } = trpc.application.getApplicationStats.useQuery({ applicationId: id }, { enabled: !!id })

  // 获取近30日趋势数据
  const { data: trendsData } = trpc.application.getApplicationTrends.useQuery(
    { applicationId: id, days: 30 },
    { enabled: !!id }
  )

  // 获取交易记录
  const { data: transactionsData, isLoading: transactionsLoading } = trpc.balance.getApplicationTransactions.useQuery(
    {
      applicationId: id,
      page: transactionPage,
      pageSize,
    },
    { enabled: !!id }
  )

  // 获取API调用记录
  const { data: apiCallsData, isLoading: apiCallsLoading } = trpc.balance.getApiCalls.useQuery(
    {
      applicationId: id,
      page: apiPage,
      pageSize,
    },
    { enabled: !!id }
  )

  // 重新生成 Secret
  const regenerateSecretMutation = trpc.application.regenerateSecret.useMutation({
    onSuccess: (data) => {
      toast.success('密钥重新生成成功！请妥善保存新密钥。')
      setNewSecret(data.secret)
      setShowSecret(true)
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const handleRegenerateSecret = () => {
    regenerateSecretMutation.mutate({ applicationId: id })
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // 这里可以添加toast提示
  }

  const formatTrafficDisplay = (trafficGB: number) => {
    if (trafficGB >= 1000) {
      return `${(trafficGB / 1000).toFixed(1)}T`
    } else if (trafficGB >= 1) {
      return `${trafficGB.toFixed(0)}G`
    } else {
      return `${(trafficGB * 1000).toFixed(0)}M`
    }
  }

  const formatApiCallsDisplay = (count: number) => {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(2)}w`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`
    } else {
      return count.toString()
    }
  }

  const formatAmount = (amount: string) => {
    return parseFloat(amount).toFixed(2)
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'RECHARGE':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'CONSUME':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'REFUND':
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      default:
        return <Minus className="h-4 w-4 text-gray-500" />
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'RECHARGE':
        return 'text-green-600'
      case 'CONSUME':
        return 'text-red-600'
      case 'REFUND':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTransactionLabel = (type: string) => {
    switch (type) {
      case 'RECHARGE':
        return '充值'
      case 'CONSUME':
        return '消费'
      case 'REFUND':
        return '退款'
      default:
        return '未知'
    }
  }

  const getCostTypeIcon = (type: string) => {
    switch (type) {
      case 'ACCOUNT_QUOTA':
        return <Shield className="h-4 w-4 text-blue-500" />
      case 'TRAFFIC':
        return <Zap className="h-4 w-4 text-yellow-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getCostTypeLabel = (type: string) => {
    switch (type) {
      case 'ACCOUNT_QUOTA':
        return '账号额度'
      case 'TRAFFIC':
        return '流量'
      default:
        return '未知'
    }
  }

  if (appLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!app) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900">应用不存在</h1>
        <p className="text-gray-600 mt-2">请检查应用ID是否正确</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">{app.name}</h1>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link to="/recharge">
              <CreditCard className="h-4 w-4 mr-2" />
              充值
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link to="/apps/$id/edit" params={{ id }}>
              <Settings className="h-4 w-4 mr-2" />
              设置
            </Link>
          </Button>
        </div>
      </div>

      {/* 标签页 */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="balance">蚁贝管理</TabsTrigger>
        </TabsList>

        {/* 概览页签 */}
        <TabsContent value="overview" className="space-y-6">
          {/* 应用信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle>应用信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-3">
                <div className="flex gap-2 items-center">
                  <label className="text-sm font-medium text-muted-foreground">应用名称：</label>
                  <div className="font-medium">{app.name}</div>
                  <div className="text-sm text-muted-foreground">（{app.description}）</div>
                </div>

                <div className="flex gap-2 items-center">
                  <label className="text-sm font-medium text-muted-foreground">appID：</label>
                  <div className="flex items-center gap-2">
                    <span className="font-mono text-sm bg-muted px-2 py-1 rounded-md border">{app.appId}</span>
                    <Button variant="ghost" size="sm" onClick={() => copyToClipboard(app.appId)} className="h-7 w-7 p-0">
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="flex gap-2 items-center">
                  <label className="text-sm font-medium text-muted-foreground">Secret：</label>
                  <div className="flex items-center gap-2">
                    <span className="font-mono text-sm bg-muted px-2 py-1 rounded-md border">
                      {newSecret && showSecret 
                        ? newSecret 
                        : showSecret 
                        ? '密钥已加密存储，无法显示' 
                        : '••••••••••••••••••••••••••••••••'
                      }
                    </span>
                    {newSecret && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowSecret(!showSecret)}
                        className="h-7 w-7 p-0"
                        data-testid="toggle-secret"
                      >
                        {showSecret ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                      </Button>
                    )}
                    {newSecret && showSecret && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(newSecret)}
                        className="h-7 w-7 p-0"
                        data-testid="copy-secret"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    )}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={regenerateSecretMutation.isPending}
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          重新生成
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>重新生成密钥</AlertDialogTitle>
                          <AlertDialogDescription>
                            您确定要重新生成应用密钥吗？旧密钥将立即失效，使用旧密钥的API调用将无法通过认证。
                            新密钥生成后请立即保存，系统不会再次显示完整密钥。
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>取消</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleRegenerateSecret}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            重新生成
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  {newSecret && (
                    <div className="text-sm text-orange-600 bg-orange-50 px-2 py-1 rounded">
                      ⚠️ 新密钥已生成，请立即保存！
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数据统计卡片 */}
          <Card>
            <CardHeader>
              <CardTitle>数据统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* 蚁贝余额 */}
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                        {balanceData?.balance || '0.00'}
                      </div>
                      <div className="text-sm text-blue-600 dark:text-blue-400 flex items-center gap-1">
                        <Coins className="h-4 w-4" />
                        蚁贝余额
                      </div>
                    </div>
                  </div>
                </div>

                {/* 账号统计 */}
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg p-6 border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-green-700 dark:text-green-300">
                        {statsData?.accountCount?.toLocaleString() || '0'}
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-400 flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        账号数量
                      </div>
                    </div>
                  </div>
                </div>

                {/* 流量统计 */}
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950 dark:to-yellow-900 rounded-lg p-6 border border-yellow-200 dark:border-yellow-800">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">
                        {formatTrafficDisplay(statsData?.trafficGB || 0)}
                      </div>
                      <div className="text-sm text-yellow-600 dark:text-yellow-400 flex items-center gap-1">
                        <Zap className="h-4 w-4" />
                        流量使用
                      </div>
                    </div>
                  </div>
                </div>

                {/* 接口调用统计 */}
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 rounded-lg p-6 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                        {formatApiCallsDisplay(statsData?.apiCallCount || 0)}
                      </div>
                      <div className="text-sm text-purple-600 dark:text-purple-400 flex items-center gap-1">
                        <Activity className="h-4 w-4" />
                        接口调用
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 趋势图表 */}
          {trendsData && trendsData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>30日趋势</CardTitle>
                <CardDescription>最近30天的使用趋势分析</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig}>
                  <LineChart
                    accessibilityLayer
                    data={trendsData}
                    margin={{
                      left: 12,
                      right: 12,
                    }}
                  >
                    <CartesianGrid vertical={false} />
                    <XAxis
                      dataKey="date"
                      tickLine={false}
                      axisLine={false}
                      tickMargin={8}
                      tickFormatter={(value) => {
                        const date = new Date(value)
                        return `${date.getMonth() + 1}/${date.getDate()}`
                      }}
                    />
                    <YAxis />
                    <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
                    <Line
                      dataKey="apiCalls"
                      type="monotone"
                      stroke="var(--color-apiCalls)"
                      strokeWidth={2}
                      dot={false}
                    />
                    <Line
                      dataKey="trafficGB"
                      type="monotone"
                      stroke="var(--color-trafficGB)"
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ChartContainer>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 蚁贝管理页签 */}
        <TabsContent value="balance" className="space-y-6">
          {/* 余额信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Coins className="h-5 w-5" />
                蚁贝余额
              </CardTitle>
              <CardDescription>蚁贝是平台虚拟货币，1元 = 1蚁贝</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary mb-4">{balanceData?.balance || '0.00'} 蚁贝</div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                <div>
                  <span className="font-medium">账号额度：</span>
                  1个额度 = 40蚁贝
                </div>
                <div>
                  <span className="font-medium">流量：</span>
                  1GB = 1蚁贝
                </div>
              </div>
              <div className="mt-4">
                <Button asChild>
                  <Link to="/recharge">
                    <CreditCard className="h-4 w-4 mr-2" />
                    立即充值
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 交易记录和API调用记录 */}
          <Tabs defaultValue="transactions" className="w-full">
            <TabsList>
              <TabsTrigger value="transactions">交易记录</TabsTrigger>
              <TabsTrigger value="api-calls">API调用记录</TabsTrigger>
            </TabsList>

            {/* 交易记录 */}
            <TabsContent value="transactions">
              <Card>
                <CardHeader>
                  <CardTitle>交易流水</CardTitle>
                  <CardDescription>查看蚁贝充值、消费和退款记录</CardDescription>
                </CardHeader>
                <CardContent>
                  {transactionsLoading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : (
                    <>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>类型</TableHead>
                            <TableHead>金额</TableHead>
                            <TableHead>交易前余额</TableHead>
                            <TableHead>交易后余额</TableHead>
                            <TableHead>描述</TableHead>
                            <TableHead>时间</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {transactionsData?.data && transactionsData.data.length > 0 ? (
                            transactionsData.data.map((transaction) => (
                              <TableRow key={transaction.id}>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    {getTransactionIcon(transaction.type)}
                                    <Badge variant="outline" className={getTransactionColor(transaction.type)}>
                                      {getTransactionLabel(transaction.type)}
                                    </Badge>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <span
                                    className={cn(
                                      'font-medium',
                                      parseFloat(transaction.amount) > 0 ? 'text-green-600' : 'text-red-600'
                                    )}
                                  >
                                    {parseFloat(transaction.amount) > 0 ? '+' : ''}
                                    {formatAmount(transaction.amount)}
                                  </span>
                                </TableCell>
                                <TableCell>{formatAmount(transaction.beforeBalance)}</TableCell>
                                <TableCell>{formatAmount(transaction.afterBalance)}</TableCell>
                                <TableCell className="max-w-xs truncate">{transaction.description || '-'}</TableCell>
                                <TableCell>{new Date(transaction.createdAt).toLocaleString('zh-CN')}</TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={6} className="text-center text-muted-foreground">
                                暂无交易记录
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>

                      {/* 分页 */}
                      {transactionsData && transactionsData.data.length > 0 && (
                        <div className="flex items-center justify-center gap-2 mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={transactionPage <= 1}
                            onClick={() => setTransactionPage(transactionPage - 1)}
                          >
                            上一页
                          </Button>
                          <span className="text-sm text-muted-foreground">第 {transactionPage} 页</span>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={!transactionsData.data || transactionsData.data.length < pageSize}
                            onClick={() => setTransactionPage(transactionPage + 1)}
                          >
                            下一页
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* API调用记录 */}
            <TabsContent value="api-calls">
              <Card>
                <CardHeader>
                  <CardTitle>API调用记录</CardTitle>
                  <CardDescription>查看API调用和自动扣费记录</CardDescription>
                </CardHeader>
                <CardContent>
                  {apiCallsLoading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : (
                    <>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>API端点</TableHead>
                            <TableHead>方法</TableHead>
                            <TableHead>扣费类型</TableHead>
                            <TableHead>扣费金额</TableHead>
                            <TableHead>状态码</TableHead>
                            <TableHead>调用时间</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {apiCallsData?.data && apiCallsData.data.length > 0 ? (
                            apiCallsData.data.map((apiCall) => (
                              <TableRow key={apiCall.id}>
                                <TableCell className="font-mono text-sm">{apiCall.endpoint}</TableCell>
                                <TableCell>
                                  <Badge variant="outline">{apiCall.method}</Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    {getCostTypeIcon(apiCall.costType)}
                                    <span>{getCostTypeLabel(apiCall.costType)}</span>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <span className="font-medium text-red-600">
                                    -{formatAmount(apiCall.costAmount)} 蚁贝
                                  </span>
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant="outline"
                                    className={apiCall.statusCode === 200 ? 'text-green-600' : 'text-red-600'}
                                  >
                                    {apiCall.statusCode || 'N/A'}
                                  </Badge>
                                </TableCell>
                                <TableCell>{new Date(apiCall.createdAt).toLocaleString('zh-CN')}</TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={6} className="text-center text-muted-foreground">
                                暂无API调用记录
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>

                      {/* 分页 */}
                      {apiCallsData && apiCallsData.data.length > 0 && (
                        <div className="flex items-center justify-center gap-2 mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={apiPage <= 1}
                            onClick={() => setApiPage(apiPage - 1)}
                          >
                            上一页
                          </Button>
                          <span className="text-sm text-muted-foreground">第 {apiPage} 页</span>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={!apiCallsData.data || apiCallsData.data.length < pageSize}
                            onClick={() => setApiPage(apiPage + 1)}
                          >
                            下一页
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </TabsContent>
      </Tabs>
    </div>
  )
}
