import { z } from 'zod'
import { router } from '@/trpc'
import type { Prisma} from '@coozf/db/schema';
import { protectedProcedure } from '@/procedure'

export const userRouter = router({
  // 获取个人信息
  user: protectedProcedure.query(async ({ ctx }) => {
    return ctx.user
  }),

  // 更新个人信息
  update: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, '姓名不能为空').optional(),
        avatar: z.string().url('请输入有效的头像地址').optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 只更新允许的字段
      const updateData: Prisma.UserUpdateInput = {}
      if (input.name) updateData.name = input.name
      if (input.avatar) updateData.avatar = input.avatar

      return ctx.repo.userRepo.update(ctx.user.id, updateData)
    }),
})
