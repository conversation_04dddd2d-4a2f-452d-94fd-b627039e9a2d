import { z } from 'zod'
import { router } from '@/trpc'
import type { Prisma } from '@coozf/db/schema'
import { CreateApplicationSchema, UpdateApplicationSchema, ApplicationListSchema } from '@coozf/db/schema'
import { generateUniqueAppId, generateAndHashSecret } from '@/lib/application'
import { formatSecretForDisplay } from '@/lib/crypto'
import { applicationProcedure, applicationWithBalanceProcedure, protectedProcedure } from '@/procedure'
import { randomBytes } from 'crypto'

export const applicationRouter = router({
  // 获取应用列表（分页）
  list: protectedProcedure.input(ApplicationListSchema).query(async ({ ctx, input }) => {
    const { page, pageSize, search } = input
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: Prisma.ApplicationWhereInput = {
      userId: ctx.user.id,
    }

    if (search) {
      where.name = { contains: search }
    }

    // 获取总数
    const total = await ctx.repo.appRepo.count(where)

    // 获取分页数据，包含余额信息
    const applications = await ctx.db.application.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        applicationBalance: true,
      },
    })

    // 格式化数据，隐藏敏感信息
    const formattedItems = applications.map((app) => ({
      ...app,
      secret: '****************************', // 隐藏 Secret
      webhookSecret: '****************************', // 隐藏 webhook Secret
      secretDisplay: '密钥已加密存储',
      balance: app.applicationBalance?.balance.toNumber() || 0,
    }))

    return {
      items: formattedItems,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }),

  // 创建应用
  create: protectedProcedure.input(CreateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 生成唯一的应用ID和密钥
    const appId = await generateUniqueAppId()
    const { secret, hashedSecret } = await generateAndHashSecret()
    const webhookSecret = `whsec_${randomBytes(16).toString('hex')}`

    const newApplication = await ctx.repo.appRepo.create({
      ...input,
      appId,
      secret: hashedSecret, // 存储加密后的 Secret
      webhookSecret, // 存储 webhook 密钥
      user: {
        connect: { id: ctx.user.id },
      },
    })

    // 创建对应的余额记录
    await ctx.repo.balanceRepo.create({
      balance: 0,
      application: {
        connect: { id: newApplication.id },
      },
    })

    // 返回应用信息，包含明文 Secret（仅此一次）
    return {
      ...newApplication,
      secret, // 明文 Secret，仅在创建时返回
      secretDisplay: formatSecretForDisplay(secret), // 格式化显示
    }
  }),

  // 获取单个应用详情
  byId: applicationWithBalanceProcedure.query(async ({ ctx }) => {
    return ctx.applicationWithBalance
  }),

  // 更新应用
  update: applicationProcedure.input(UpdateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 只更新允许的字段
    const updateData: Prisma.ApplicationUpdateInput = {}
    if (input.name) updateData.name = input.name
    if (input.description !== undefined) updateData.description = input.description
    if (input.webhookUrl !== undefined) updateData.webhookUrl = input.webhookUrl
    if (input.status) updateData.status = input.status

    const updated = await ctx.repo.appRepo.update(ctx.applicationId, updateData)

    return updated
  }),

  // 删除应用
  delete: applicationProcedure.mutation(async ({ ctx }) => {
    await ctx.repo.appRepo.delete(ctx.applicationId)

    return { success: true }
  }),

  // 重新生成 Secret
  regenerateSecret: applicationProcedure.mutation(async ({ ctx }) => {
    // 生成新的 Secret
    const { secret, hashedSecret } = await generateAndHashSecret()

    // 更新数据库中的 Secret
    const updated = await ctx.repo.appRepo.update(ctx.applicationId, {
      secret: hashedSecret,
    })

    // 返回新的明文 Secret（仅此一次）
    return {
      ...updated,
      secret, // 明文 Secret，仅在重新生成时返回
      secretDisplay: formatSecretForDisplay(secret),
      message: '密钥已重新生成，请妥善保存，此密钥不会再次显示',
    }
  }),

  // 获取应用统计数据
  getApplicationStats: applicationProcedure.query(async ({ ctx }) => {
    // 获取API调用统计
    const apiCalls = await ctx.repo.apiCallRepo.findByApplicationId(ctx.applicationId)

    let accountQuotaCount = 0
    let trafficGB = 0

    apiCalls.forEach((call) => {
      if (call.costType === 'ACCOUNT_QUOTA') {
        accountQuotaCount += 1
      }
      if (call.costType === 'TRAFFIC') {
        trafficGB += Number(call.costAmount)
      }
    })

    // 获取充值总额
    const transactions = await ctx.repo.transactionRepo.findByApplicationId(ctx.applicationId, {
      type: 'RECHARGE',
    })

    const totalRecharge = transactions.reduce((sum, transaction) => {
      return sum + Number(transaction.amount)
    }, 0)

    return {
      apiCallCount: apiCalls.length,
      accountCount: accountQuotaCount,
      trafficGB,
      totalRecharge,
    }
  }),

  // 获取应用趋势数据
  getApplicationTrends: applicationProcedure
    .input(
      z.object({
        days: z.number().min(1).max(90).default(30),
      }),
    )
    .query(async ({ ctx, input }) => {
      const daysAgo = new Date()
      daysAgo.setDate(daysAgo.getDate() - input.days)

      // 获取指定时间范围内的API调用记录
      const apiCalls = await ctx.repo.apiCallRepo.findByApplicationId(ctx.applicationId)

      // 过滤指定时间范围内的记录
      const filteredCalls = apiCalls.filter((call) => new Date(call.createdAt) >= daysAgo)

      // 按日期分组统计
      const dailyStatsMap = new Map<
        string,
        {
          apiCalls: number
          accountCount: number
          trafficGB: number
        }
      >()

      filteredCalls.forEach((call) => {
        const dateStr = new Date(call.createdAt).toISOString().split('T')[0]
        const existing = dailyStatsMap.get(dateStr) || { apiCalls: 0, accountCount: 0, trafficGB: 0 }

        existing.apiCalls += 1
        if (call.costType === 'ACCOUNT_QUOTA') {
          existing.accountCount += 1
        }
        if (call.costType === 'TRAFFIC') {
          existing.trafficGB += Number(call.costAmount)
        }

        dailyStatsMap.set(dateStr, existing)
      })

      // 填充没有数据的日期
      const trends: Array<{
        date: string
        apiCalls: number
        accountCount: number
        trafficGB: number
      }> = []

      for (let i = input.days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]

        const existing = dailyStatsMap.get(dateStr)
        trends.push({
          date: dateStr,
          apiCalls: existing?.apiCalls || 0,
          accountCount: existing?.accountCount || 0,
          trafficGB: existing?.trafficGB || 0,
        })
      }

      return trends
    }),
})
