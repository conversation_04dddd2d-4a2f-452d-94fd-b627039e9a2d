// Prisma schema file for MySQL database
// 从 Drizzle ORM + PostgreSQL 迁移到 Prisma ORM + MySQL

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id            String   @id @default(cuid())
  email         String?  @unique @db.VarChar(255)
  phone         String?  @unique @db.VarChar(20)
  password      String?  @db.VarChar(255)
  name          String?  @db.VarChar(100)
  phoneVerified Boolean  @default(false)
  emailVerified Boolean  @default(false)
  avatar        String?  @db.VarChar(500)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关系
  applications Application[]
  orders       Order[]

  @@map("users")
}

// 应用表
model Application {
  id            String            @id @default(cuid())
  userId        String
  appId         String            @unique @db.VarChar(64) // 公开的应用ID
  name          String            @db.VarChar(100)
  description   String?           @db.Text
  secret        String            @db.VarChar(255) // 加密存储的应用密钥
  status        ApplicationStatus @default(ACTIVE) // active, suspended, deleted
  webhookUrl    String?           @db.VarChar(500) // 应用的webhook回调地址
  webhookSecret String            @db.VarChar(255) // 用于签名的webhook密钥
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // 关系
  user                User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  authAccounts        AuthAccount[]
  applicationBalance  ApplicationBalance?
  transactions        Transaction[]
  apiCalls            ApiCall[]
  orders              Order[]

  @@index([userId])
  @@index([appId])
  @@index([status])
  @@map("applications")
}

// 应用状态枚举
enum ApplicationStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

// 授权账号表
model AuthAccount {
  id             String   @id @default(cuid())
  appId          String
  platform       String   @db.VarChar(20) // xiaohongshu, douyin, kuaishou 等
  platformUserId String   @db.VarChar(100)
  userInfo       Json? // 用户基本信息
  state          String?  @db.VarChar(100) // OAuth状态参数
  scope          String?  @db.VarChar(200) // 授权范围
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // 关系
  application Application @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@index([appId])
  @@index([platform])
  @@index([platformUserId])
  @@map("auth_accounts")
}

// 应用蚁贝账户表
model ApplicationBalance {
  id            String   @id @default(cuid())
  applicationId String   @unique
  balance       Decimal  @default(0.00) @db.Decimal(10, 2)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("application_balances")
}

// 交易流水表
model Transaction {
  id            String          @id @default(cuid())
  applicationId String
  type          TransactionType // RECHARGE, CONSUME, REFUND
  amount        Decimal         @db.Decimal(10, 2)
  beforeBalance Decimal         @db.Decimal(10, 2)
  afterBalance  Decimal         @db.Decimal(10, 2)
  description   String?         @db.VarChar(500)
  relatedId     String
  relatedType   String          @db.VarChar(20) // 'order' | 'api_call' | 'traffic' | 'refund'
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([type])
  @@index([createdAt])
  @@map("transactions")
}

// 交易类型枚举
enum TransactionType {
  RECHARGE
  CONSUME
  REFUND
}

// API调用记录表
model ApiCall {
  id            String      @id @default(cuid())
  applicationId String
  endpoint      String      @db.VarChar(255)
  method        String      @db.VarChar(10)
  costType      ApiCostType // TRAFFIC, API_CALL
  costAmount    Decimal     @db.Decimal(10, 2)
  statusCode    Int?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([endpoint])
  @@index([costType])
  @@index([createdAt])
  @@map("api_calls")
}

// API调用成本类型枚举
enum ApiCostType {
  ACCOUNT_QUOTA
  TRAFFIC
}

// 订单表
model Order {
  id               String        @id @default(cuid())
  orderNo          String        @unique @db.VarChar(32) // 订单号
  userId           String
  applicationId    String
  antCoins         Decimal       @db.Decimal(10, 2) // 蚁贝数量
  amount           Decimal       @db.Decimal(10, 2) // 对应金额
  source           OrderSource   @default(SYSTEM) // 来源：SYSTEM(系统订单)
  type             OrderType // 类型：PURCHASE(购买), GIFT(赠送)
  paymentMethod    PaymentMethod @default(BANK_TRANSFER) // 付款方式：BANK_TRANSFER(对公转账)
  status           OrderStatus   @default(COMPLETED) // 状态：PENDING(待付款), COMPLETED(已完成), CANCELLED(已取消)
  invoiceRequested Boolean       @default(false) // 发票申请标识
  remarks          String?       @db.VarChar(500) // 备注说明
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt

  // 关系
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([orderNo])
  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([createdAt])
  @@map("orders")
}

// 订单来源枚举
enum OrderSource {
  SYSTEM
}

// 订单类型枚举
enum OrderType {
  PURCHASE
  GIFT
}

// 付款方式枚举
enum PaymentMethod {
  BANK_TRANSFER
}

// 订单状态枚举
enum OrderStatus {
  PENDING
  COMPLETED
  CANCELLED
}
