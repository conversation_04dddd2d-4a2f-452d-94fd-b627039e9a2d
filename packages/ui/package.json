{"name": "@coozf/ui", "version": "0.0.0", "private": true, "license": "MIT", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist/**", "dist"], "scripts": {"build": "bunchee", "dev": "bunchee --watch", "check-types": "tsc --noEmit", "lint": "eslint src/", "test": "jest", "ui:add": "shadcn add"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.518.0", "next-themes": "^0.4.6", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@coozf/eslint-config": "workspace:*", "@coozf/typescript-config": "workspace:*", "@types/node": "^22.15.3", "bunchee": "^6.4.0", "eslint": "^9.30.0", "jest": "^29.7.0", "typescript": "5.8.2", "shadcn": "^2.1.6"}, "peerDependencies": {"@types/react": ">=18", "@types/react-dom": ">=18", "react": ">=18", "react-dom": ">=18", "tailwindcss": ">=3.0.0"}}